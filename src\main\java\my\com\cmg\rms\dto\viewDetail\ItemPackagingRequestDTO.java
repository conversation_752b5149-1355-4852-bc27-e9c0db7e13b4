package my.com.cmg.rms.dto.viewDetail;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ItemPackagingRequestDTO {
  private Long itemPackagingReqSeqno;
  private Long itemSeqno;
  private String itemName;
  private String itemCode;
  private String itemPackagingName;
  private Long skuSeqno;
  private Long pkuSeqno;
  private String conversionFactor;
  private String packagingDesc;
  private String productList;
  private Long productSeqno;
  private String productName;
  private String manufacturedName;
  private String importerName;
  private String manufacturedAddress;
  private String importerAddress;
  private String gtinNo;
  private String mdaNo;
}
