package my.com.cmg.rms.dto.viewDetail;

import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ItemMasterRequestDTO {
  private Long itemReqSeqno;
  private String itemGroupCode;
  private Long genericNameSeqno;
  private String otherActiveIngredient;
  private String strength;
  private Long dosageSeqno;
  private String itemName;
  private Long itemCatSeqno;
  private Long itemSubgroupSeqno;
  private Long freqSeqno;
  private String administrationRoute;
  private String drugIndication;
  private String rpItemTypeCode;
  private Long itemPackagingSeqno;
  private Long skuSeqno;
  private Long pkuSeqno;
  private BigDecimal conversionFactorNum;
  private String packagingDesc;
  private String mdcNo;
  private Long productSeqno;
  private String productName;
  private String manufacturedName;
  private String importerName;
  private String manufacturedAddress;
  private String importerAddress;
  private String gtinNo;
  private String mdaNo;
}
