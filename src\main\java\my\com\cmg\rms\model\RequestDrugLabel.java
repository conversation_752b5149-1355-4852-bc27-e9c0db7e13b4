package my.com.cmg.rms.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "rm_request_drug_label")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class RequestDrugLabel extends BaseEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "drug_label_seq")
  @SequenceGenerator(
      name = "drug_label_seq",
      sequenceName = "rm_request_drug_label_seq",
      allocationSize = 1)
  @Column(name = "drug_req_seqno")
  private Long drugReqSeqno;

  @Column(name = "item_seqno")
  private Long itemSeqno;

  @Column(name = "indication_code")
  private String indicationCode;

  @Column(name = "indication_description")
  private String indicationDescription;

  @Column(name = "guideline")
  private String guideline;

  @Column(name = "status")
  private String status;
}
