package my.com.cmg.rms.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.*;

@Entity
@Table(name = "rm_request_item_atc")
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class RequestItemAtc extends BaseEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "request_item_atc_seq_gen")
  @SequenceGenerator(
      name = "request_item_atc_seq_gen",
      sequenceName = "rm_request_item_atc_seq",
      allocationSize = 1)
  @Column(name = "atc_req_seqno")
  private Long atcReqSeqno;

  @Column(name = "item_seqno")
  private Long itemSeqno;

  @Column(name = "atc_req_code")
  private String atcReqCode;

  @Column(name = "atc_req_name")
  private String atcReqName;

  @Column(name = "atc_req_desc")
  private String atcReqDesc;

  @Column(name = "atc_defined_daily_dose")
  private String atcDefinedDailyDose;

  @Column(name = "status")
  private String status;
}
