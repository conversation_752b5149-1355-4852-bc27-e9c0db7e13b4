package my.com.cmg.rms.service;

import java.util.List;
import my.com.cmg.rms.dto.AdminItemMasterDetails.AdminItemMasterDetailDTO;
import my.com.cmg.rms.dto.AdminItemMasterDetails.AdminItemPackagingDTO;
import my.com.cmg.rms.dto.AdminItemMasterDetails.AdminItemRouteDTO;
import my.com.cmg.rms.dto.AdminItemMasterDetails.DrugLabelDTO;
import my.com.cmg.rms.dto.AdminItemMasterDetails.ItemMasterMethodStatusDTO;
import my.com.cmg.rms.dto.AdminItemMasterDetails.RequestItemAtcDTO;
import my.com.cmg.rms.dto.AdminItemMasterDetails.RequestItemFrequencyDTO;
import my.com.cmg.rms.dto.AssignRequestDTO;
import my.com.cmg.rms.dto.AssignmentHistoryDTO;
import my.com.cmg.rms.dto.RequestStatusDTO;
import my.com.cmg.rms.dto.paging.PaginationRequestDTO;
import my.com.cmg.rms.dto.paging.PaginationResponseDTO;
import my.com.cmg.rms.dto.requestDetail.SaveRequestDTO;
import my.com.cmg.rms.dto.requestListing.RequestListDTO;
import my.com.cmg.rms.dto.requestListing.RequestListSearchDTO;
import my.com.cmg.rms.dto.viewDetail.ViewRequestDTO;

public interface IRequestService {

  Long save(SaveRequestDTO dto);

  List<RequestListDTO> getRequestList(RequestListSearchDTO requestDTO, PaginationRequestDTO pgDTO);

  PaginationResponseDTO getRequestListPages(RequestListSearchDTO requestDTO, Long size);

  ViewRequestDTO getViewRequest(Long requestHdrSeqno);

  List<AssignmentHistoryDTO> getAssignmentLog(Long requestHdrSeqno);

  void update(Long requestHdrSeqno, SaveRequestDTO dto);

  void confirmRequest(Long requestHdrSeqno);

  void assignRequest(AssignRequestDTO dto);

  void rejectRequest(RequestStatusDTO dto);

  void approveRequest(RequestStatusDTO dto);

  void saveAdminItemMasterDetail(AdminItemMasterDetailDTO dto);

  List<AdminItemPackagingDTO> getPackagingByItemSeqno(Long itemSeqno);

  void saveAllPackaging(List<AdminItemPackagingDTO> dtoList);

  void saveAllItemRoutes(Long itemSeqno, List<AdminItemRouteDTO> dtoList);

  List<AdminItemRouteDTO> getItemRoutesByItemSeqno(Long itemSeqno);

  List<AdminItemRouteDTO> getAllItemRoutes();

  List<RequestItemFrequencyDTO> getItemFrequenciesByItemSeqno(Long itemSeqno);

  List<RequestItemFrequencyDTO> getAllItemFrequencies();

  void saveAllItemFrequencies(Long itemSeqno, List<RequestItemFrequencyDTO> dtoList);

  List<RequestItemAtcDTO> getItemAtcByItemSeqno(Long itemSeqno);

  List<RequestItemAtcDTO> getAllItemAtc();

  void saveAllItemAtc(Long itemSeqno, List<RequestItemAtcDTO> dtoList);

  List<DrugLabelDTO> getDrugLabelByItemSeqno(Long itemSeqno);

  List<DrugLabelDTO> getAllDrugLabel();

  void saveAllDrugLabel(Long itemSeqno, List<DrugLabelDTO> dtoList);

  void updateItemMasterMethodStatus(ItemMasterMethodStatusDTO dto);
}
