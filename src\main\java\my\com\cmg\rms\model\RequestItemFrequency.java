package my.com.cmg.rms.model;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Table(name = "rm_request_item_frequency")
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class RequestItemFrequency extends BaseEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "request_item_frequency_seq_gen")
  @SequenceGenerator(
      name = "request_item_frequency_seq_gen",
      sequenceName = "rm_request_item_frequency_seq",
      allocationSize = 1)
  @Column(name = "frequency_req_seqno")
  private Long frequencyReqSeqno;

  @Column(name = "item_seqno")
  private Long itemSeqno;

  @Column(name = "frequency_code")
  private String frequencyCode;

  @Column(name = "frequency_desc")
  private String frequencyDesc;

  @Column(name = "description_local_lang")
  private String frequencyLocalDesc;

  @Column(name = "guideline")
  private String guideline;

  @Column(name = "status")
  private String status;
}
