package my.com.cmg.rms.security;

import java.util.List;
import my.com.cmg.rms.exception.ExceptionCode;
import my.com.cmg.rms.exception.RmsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;

public class CurrentUserUtil {

  public static Long getCurrentUserId() {
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
    if (authentication instanceof JwtAuthenticationToken jwtAuth) {
      Jwt jwt = jwtAuth.getToken();

      String userIdStr = jwt.getClaimAsString("preferred_username");
      if (userIdStr == null) {
        throw new RmsException(ExceptionCode.FORBIDDEN, "Invalid user ID in token");
      }

      try {
        return Long.parseLong(userIdStr);
      } catch (NumberFormatException e) {
        throw new RmsException(
            ExceptionCode.FORBIDDEN, "User ID is not a valid number: " + userIdStr);
      }
    }

    throw new RmsException(ExceptionCode.FORBIDDEN, "Invalid authentication type");
  }

  public static String getCurrentUserName() {
    Jwt jwt = (Jwt) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
    return jwt.getClaimAsString("preferred_username");
  }

  public static String getCurrentUserDivision() {
    Jwt jwt = (Jwt) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
    return jwt.getClaimAsString("division");
  }

  public static boolean isAdmin() {
    Jwt jwt = (Jwt) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
    List<String> roles = jwt.getClaimAsStringList("role");
    return roles != null && roles.contains("ADMIN");
  }
}
