package my.com.cmg.rms.repository.jpa;

import java.util.List;
import java.util.Optional;
import my.com.cmg.rms.model.RequestDtl;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface RequestDtlRepository extends JpaRepository<RequestDtl, Long> {

  List<RequestDtl> findByRequestHdrRequestHdrSeqno(Long requestHdrSeqno);

  @Query(
      "SELECT MAX(r.transSeqno) FROM RequestDtl r WHERE r.requestHdr.requestHdrSeqno = :hdrSeqno")
  Optional<Long> findMaxTransSeqnoByRequestHdr(@Param("hdrSeqno") Long hdrSeqno);
}
