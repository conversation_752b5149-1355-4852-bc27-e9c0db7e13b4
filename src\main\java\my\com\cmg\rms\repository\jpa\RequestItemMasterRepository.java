package my.com.cmg.rms.repository.jpa;

import jakarta.transaction.Transactional;
import java.util.Optional;
import my.com.cmg.rms.model.RequestItemMaster;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface RequestItemMasterRepository extends JpaRepository<RequestItemMaster, Long> {
  Optional<RequestItemMaster> findByItemReqSeqno(Long itemReqSeqno);

  @Modifying
  @Transactional
  @Query(
      "UPDATE RequestItemMaster r SET r.methodStatus = :methodStatus WHERE r.transSeqno ="
          + " :transSeqno")
  void updateMethodStatus(
      @Param("transSeqno") Long transSeqno, @Param("methodStatus") String methodStatus);
}
