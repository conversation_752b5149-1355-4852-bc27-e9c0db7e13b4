package my.com.cmg.rms.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "rm_request_supplier")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class RequestSupplier extends BaseEntity {

  @Id
  @Column(name = "supplier_req_seqno", unique = true, nullable = false)
  @SequenceGenerator(
      name = "request_supplier_seq_no",
      sequenceName = "rm_request_supplier_seq",
      allocationSize = 1)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "request_supplier_seq_no")
  private Long supplierReqSeqno;

  @Column(name = "supplier_req_code", length = 50)
  private String supplierReqCode;

  @Column(name = "supplier_req_name", length = 100)
  private String supplierReqName;

  @Column(name = "supplier_req_type", length = 10)
  private String supplierReqType;

  @Column(name = "supplier_req_type_desc", length = 50)
  private String supplierReqTypeDesc;

  @Column(name = "company_reg_no", length = 40)
  private String companyRegNo;

  @Column(name = "reg_expiry_date")
  private LocalDateTime regExpiryDate;

  @Column(name = "trs_reg_no", length = 40)
  private String trsRegNo;

  @Column(name = "company_status", length = 20)
  private String companyStatus;

  @Column(name = "company_status_desc", length = 100)
  private String companyStatusDesc;

  @Column(name = "address1", length = 100)
  private String address1;

  @Column(name = "address2", length = 100)
  private String address2;

  @Column(name = "address3", length = 100)
  private String address3;

  @Column(name = "city", length = 30)
  private String city;

  @Column(name = "state", length = 20)
  private String state;

  @Column(name = "state_desc", length = 100)
  private String stateDesc;

  @Column(name = "postcode", length = 5)
  private String postcode;

  @Column(name = "country", length = 48)
  private String country;

  @Column(name = "mobile_phone", length = 20)
  private String mobilePhone;

  @Column(name = "email", length = 50)
  private String email;

  @Column(name = "contact_person", length = 30)
  private String contactPerson;

  @Column(name = "contact_no", length = 30)
  private String contactNo;

  @Column(name = "trans_seqno")
  private Long transSeqno;
}
