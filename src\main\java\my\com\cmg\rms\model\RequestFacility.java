package my.com.cmg.rms.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "rm_request_facility")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class RequestFacility extends BaseEntity {

  @Id
  @Column(name = "facility_req_seqno", unique = true, nullable = false)
  @SequenceGenerator(
      name = "request_facility_seq_no",
      sequenceName = "rm_request_facility_seq",
      allocationSize = 1)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "request_facility_seq_no")
  private Long facilityReqSeqno;

  @Column(name = "facility_req_code", length = 50)
  private String facilityReqCode;

  @Column(name = "facility_req_name", length = 100)
  private String facilityReqName;

  @Column(name = "facility_req_type", length = 10)
  private String facilityReqType;

  @Column(name = "facility_req_type_desc", length = 50)
  private String facilityReqTypeDesc;

  @Column(name = "facility_req_group", length = 10)
  private String facilityReqGroup;

  @Column(name = "facility_req_group_desc", length = 50)
  private String facilityReqGroupDesc;

  @Column(name = "ministry", length = 10)
  private String ministry;

  @Column(name = "ministry_desc", length = 50)
  private String ministryDesc;

  @Column(name = "facility_req_category", length = 10)
  private String facilityReqCategory;

  @Column(name = "facility_req_category_desc", length = 50)
  private String facilityReqCategoryDesc;

  @Column(name = "address1", length = 100)
  private String address1;

  @Column(name = "address2", length = 100)
  private String address2;

  @Column(name = "address3", length = 100)
  private String address3;

  @Column(name = "city", length = 100)
  private String city;

  @Column(name = "postcode", length = 10)
  private String postcode;

  @Column(name = "state", length = 20)
  private String state;

  @Column(name = "state_desc", length = 100)
  private String stateDesc;

  @Column(name = "country", length = 48)
  private String country;

  @Column(name = "mobile_phone", length = 20)
  private String mobilePhone;

  @Column(name = "email", length = 50)
  private String email;

  @Column(name = "contact_person", length = 250)
  private String contactPerson;

  @Column(name = "contact_no", length = 250)
  private String contactNo;

  @Column(name = "trans_seqno")
  private Long transSeqno;
}