package my.com.cmg.rms.model;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "rm_request_assignment_history")
@Getter
@Setter
public class RequestAssignmentHistory extends BaseEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long assignId;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "request_hdr_seqno", nullable = false)
  private RequestHdr requestHdr;

  @Column(name = "assigned_to", length = 20, nullable = false)
  private String assignedTo;

  @Column(name = "assigned_by", nullable = false)
  private Long assignedBy;

  @Column(name = "assigned_date")
  private LocalDateTime assignedDate;

  @Column(name = "remarks", length = 200)
  private String remarks;
}
