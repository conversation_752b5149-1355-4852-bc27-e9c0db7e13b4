package my.com.cmg.rms.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "rm_request_item_route")
public class RequestItemRoute extends BaseEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "request_item_route_seq_gen")
  @SequenceGenerator(
      name = "request_item_route_seq_gen",
      sequenceName = "rm_request_item_route_seq",
      allocationSize = 1)
  @Column(name = "item_route_req_seqno")
  private Long itemRouteReqSeqno;

  @Column(name = "item_seqno", nullable = false)
  private Long itemSeqno;

  @Column(name = "route_code", nullable = false)
  private String routeCode;

  @Column(name = "route_description")
  private String routeDescription;

  @Column(name = "route_factor")
  private BigDecimal routeFactor;

  @Column(name = "local_description")
  private String localDescription;

  @Column(name = "status")
  private String status;
}
