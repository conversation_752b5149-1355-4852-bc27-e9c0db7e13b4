package my.com.cmg.rms.repository.jpa;

import java.util.List;
import my.com.cmg.rms.model.RequestAssignmentHistory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface RequestAssignmentHistoryRepository
    extends JpaRepository<RequestAssignmentHistory, Long> {

  List<RequestAssignmentHistory> findByRequestHdr_RequestHdrSeqnoOrderByAssignedDateDesc(
      Long requestHdrSeqno);
}
