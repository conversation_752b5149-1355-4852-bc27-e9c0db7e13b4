package my.com.cmg.rms.dto.viewDetail;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class FacilityMasterRequestDTO {
  private Long facilityReqSeqno;
  private String facilityReqName;
  private String facilityReqGroup;
  private String ministry;
  private String facilityReqCategory;
  private String facilityReqType;
  private String address1;
  private String address2;
  private String address3;
  private String city;
  private String postcode;
  private String state;
  private String country;
  private String mobilePhone;
  private String email;
  private String contactPerson;
  private String contactNo;
}
