package my.com.cmg.rms.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "rm_request_hdr")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class RequestHdr extends BaseEntity {

  @Id
  @Column(name = "request_hdr_seqno", unique = true, nullable = false)
  @SequenceGenerator(
      name = "request_hdr_seq_no",
      sequenceName = "rm_request_hdr_seq",
      allocationSize = 1)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "request_hdr_seq_no")
  private Long requestHdrSeqno;

  @Column(name = "request_no", length = 10, nullable = false)
  private String requestNo;

  @Column(name = "request_type", length = 1, nullable = false)
  private String requestType;

  @Column(name = "request_type_desc", length = 20)
  private String requestTypeDesc;

  @Column(name = "category", length = 10, nullable = false)
  private String category;

  @Column(name = "category_desc", length = 50)
  private String categoryDesc;

  @Column(name = "sub_category", length = 10, nullable = false)
  private String subCategory;

  @Column(name = "sub_category_desc", length = 50)
  private String subCategoryDesc;

  @Column(name = "title", length = 100, nullable = false)
  private String title;

  @Column(name = "reference", length = 200)
  private String reference;

  @Column(name = "intention", length = 3)
  private String intention;

  @Column(name = "intention_desc", length = 50)
  private String intentionDesc;

  @Column(name = "reason", length = 200, nullable = false)
  private String reason;

  @Column(name = "facility_seqno", nullable = false)
  private Long facilitySeqno;

  @Column(name = "facility_name", length = 100)
  private String facilityName;

  @Column(name = "requested_by_seqno", nullable = false)
  private Long requestedBySeqno;

  @Column(name = "requested_by_name", length = 100)
  private String requestedByName;

  @Column(name = "requested_date", nullable = false)
  private LocalDateTime requestedDate;

  @Column(name = "assigned_to", length = 20)
  private String assignedTo;

  @Column(name = "assigned_to_desc", length = 100)
  private String assignedToDesc;

  @Column(name = "assigned_from", length = 20)
  private String assignedFrom;

  @Column(name = "assigned_from_desc", length = 100)
  private String assignedFromDesc;

  @Column(name = "status", length = 10)
  private String status;

  @Column(name = "status_desc", length = 50)
  private String statusDesc;

  @Column(name = "reject_reason", length = 200)
  private String rejectReason;
}