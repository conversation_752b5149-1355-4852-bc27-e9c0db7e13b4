package my.com.cmg.rms.dto.AdminItemMasterDetails;

import java.math.BigDecimal;
import lombok.Data;

@Data
public class AdminItemMasterDetailDTO {
  private Long itemReqSeqno;
  private String itemGroupCode;
  private String itemGroupDesc;
  private Long itemCatSeqno;
  private String itemCategoryCode;
  private String categoryDesc;
  private Long itemSubgroupSeqno;
  private String itemSubgroupCode;
  private String subgroupDesc;
  private Long skuSeqno;
  private String skuAbbr;
  private String itemNameAdmin;
  private String voteObject;
  private String itemDispensable;
  private String drugLabel;
  private Long instructionSeqno;
  private String instruction;
  private Long specialInstructionSeqno;
  private String specialInstruction;
  private Long cautionarySeqno;
  private String cautionary;
  private String localLang1;
  private String localLang2;
  private String localLang3;
  private BigDecimal strengthValue;
  private String strengthUnit;
  private String snomedCtCode;
  private String snomedCtName;
  private String snomedCtSystem;
  private BigDecimal quantity;
  private String dosageGroupCode;
  private String dosageGroupDesc;
  private String drugTypeCode;
  private String drugTypeDesc;
  private String specialOrderConfiguration;
  private String drugSchedule;
  private String drugScheduleNew;
  private String doseToBeDisplayed;
  private String nationalEssentialMedicine;
  private String medicationAssistedTheraphy;
  private String nonCommunicableDisease;
  private String biologicalProductLotRelease;
  private String methodStatus;
  private Long transSeqno;
}
