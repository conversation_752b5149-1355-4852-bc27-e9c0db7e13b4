package my.com.cmg.rms.dto.viewDetail;

import java.time.LocalDate;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SupplierMasterRequestDTO {
  private Long supplierReqSeqno;
  private String supplierReqName;
  private String companyRegNo;
  private LocalDate regExpiryDate;
  private String trsRegNo;
  private String companyStatus;
  private String address1;
  private String address2;
  private String address3;
  private String city;
  private String postcode;
  private String state;
  private String country;
  private String mobilePhone;
  private String email;
  private String contactPerson;
  private String contactNo;
  private String supplierReqCode;
  private String supplierReqType;
  private String supplierReqTypeDesc;
  private String companyStatusDesc;
  private String stateDesc;
}
